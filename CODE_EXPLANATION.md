# 📚 Code Explanation - Flask Phishing Detection App

## 🎯 Overview
This document provides detailed explanations of every component in the phishing detection application, including command-line usage and code walkthrough.

## 📄 File-by-File Breakdown

### 1. `app.py` - Main Flask Application

#### Purpose
The core web application that handles HTTP requests, loads the ML model, and serves predictions.

#### Key Components

##### Imports and Setup
```python
# === FLASK PHISHING DETECTION APP ===
from flask import Flask, render_template, request, jsonify, flash  # Web framework
import pickle           # For loading saved ML models
import os               # File system operations
from urllib.parse import urlparse  # URL parsing utilities
import re               # Regular expressions for validation
import logging          # Error and info logging
```

**Command to test imports:**
```bash
python -c "import flask, pickle, os, urllib.parse, re, logging; print('All imports successful')"
```

##### Model Loading Function
```python
def load_model():
    global model
    model_path = 'phishing_model.pkl'  # Model file location
    # ... loading logic
```

**Command to test model loading:**
```bash
python -c "
import pickle
import os
if os.path.exists('phishing_model.pkl'):
    with open('phishing_model.pkl', 'rb') as f:
        model = pickle.load(f)
    print('Model loaded successfully')
else:
    print('Model file not found')
"
```

##### Feature Extraction Function
```python
def extract_url_features(url):
    # Extracts 5 numerical features from URL:
    # 1. URL length
    # 2. Number of '@' symbols
    # 3. Number of '-' symbols  
    # 4. HTTPS presence (1/0)
    # 5. Dots in domain
```

**Command to test feature extraction:**
```bash
python -c "
from urllib.parse import urlparse
url = 'https://google.com'
parsed = urlparse(url)
features = [len(url), url.count('@'), url.count('-'), 1 if url.startswith('https') else 0, parsed.netloc.count('.')]
print(f'Features for {url}: {features}')
"
```

##### Web Routes
- `@app.route('/')` - Homepage with input form
- `@app.route('/predict', methods=['POST'])` - Form submission handler
- `@app.route('/api/predict', methods=['POST'])` - JSON API endpoint

### 2. `create_sample_model.py` - Model Generation

#### Purpose
Creates a sample machine learning model for testing when you don't have a real trained model.

#### Usage Commands
```bash
# Generate the sample model
python create_sample_model.py

# Check if model was created
ls -la phishing_model.pkl  # Mac/Linux
dir phishing_model.pkl     # Windows

# Test the generated model
python -c "
import pickle
with open('phishing_model.pkl', 'rb') as f:
    model = pickle.load(f)
print(f'Model type: {type(model)}')
print(f'Model features: {model.n_features_in_}')
"
```

### 3. `test_api.py` - API Testing Script

#### Purpose
Automated testing script to verify the API endpoint works correctly.

#### Usage Commands
```bash
# Run API tests
python test_api.py

# Test specific URL via command line
python -c "
import requests
response = requests.post('http://localhost:5000/api/predict', json={'url': 'https://google.com'})
print(response.json())
"

# Test with curl (if available)
curl -X POST http://localhost:5000/api/predict \
  -H 'Content-Type: application/json' \
  -d '{"url": "https://google.com"}'
```

### 4. `requirements.txt` - Dependencies

#### Purpose
Lists all Python packages needed for the project.

#### Usage Commands
```bash
# Install all dependencies
pip install -r requirements.txt

# Check installed packages
pip list

# Check specific package version
pip show flask

# Create requirements file (if updating)
pip freeze > requirements.txt

# Install in virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # Mac/Linux
venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

### 5. HTML Templates (`templates/` folder)

#### `base.html` - Base Template
```html
<!-- Bootstrap CSS framework for styling -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
```

#### `index.html` - Main Form
Contains the URL input form with validation.

#### `result.html` - Results Page
Displays prediction results with styling based on outcome.

**Command to validate HTML:**
```bash
# Check template syntax (if you have html5lib)
python -c "
from flask import Flask, render_template
app = Flask(__name__)
with app.app_context():
    try:
        render_template('index.html')
        print('Templates are valid')
    except Exception as e:
        print(f'Template error: {e}')
"
```

## 🔧 Command-Line Operations

### Development Workflow

#### 1. Initial Setup
```bash
# Clone or create project directory
mkdir phishing-detection
cd phishing-detection

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # Mac/Linux
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

#### 2. Generate Model (First Time)
```bash
# Create sample model
python create_sample_model.py

# Verify model creation
python -c "import os; print('Model exists:', os.path.exists('phishing_model.pkl'))"
```

#### 3. Start Development Server
```bash
# Run Flask app in debug mode
python app.py

# Alternative: Use Flask CLI
export FLASK_APP=app.py  # Mac/Linux
set FLASK_APP=app.py     # Windows CMD
$env:FLASK_APP="app.py"  # Windows PowerShell
flask run --debug
```

#### 4. Test Application
```bash
# Test web interface
python -c "import webbrowser; webbrowser.open('http://localhost:5000')"

# Test API programmatically
python test_api.py

# Manual API test with Python
python -c "
import requests
import json
data = {'url': 'https://example.com'}
response = requests.post('http://localhost:5000/api/predict', json=data)
print(json.dumps(response.json(), indent=2))
"
```

### Production Deployment

#### Using Gunicorn (Linux/Mac)
```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Run in background
nohup gunicorn -w 4 -b 0.0.0.0:5000 app:app &
```

#### Using Waitress (Windows)
```bash
# Install Waitress
pip install waitress

# Run with Waitress
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

### Debugging Commands

#### Check Application Health
```bash
# Test if server is running
curl -I http://localhost:5000

# Check specific endpoint
curl -X POST http://localhost:5000/api/predict \
  -H "Content-Type: application/json" \
  -d '{"url": "test.com"}' \
  -v  # Verbose output

# Test with Python requests
python -c "
import requests
try:
    response = requests.get('http://localhost:5000')
    print(f'Status: {response.status_code}')
    print('Server is running')
except:
    print('Server is not responding')
"
```

#### Monitor Logs
```bash
# Run with verbose logging
FLASK_ENV=development python app.py

# Check Python warnings
python -W all app.py

# Monitor file changes (if using watchdog)
pip install watchdog
python app.py  # Auto-reloads on file changes
```

#### Performance Testing
```bash
# Install testing tools
pip install requests

# Simple load test
python -c "
import requests
import time
start = time.time()
for i in range(10):
    response = requests.post('http://localhost:5000/api/predict', 
                           json={'url': f'https://test{i}.com'})
    print(f'Request {i+1}: {response.status_code}')
end = time.time()
print(f'Total time: {end-start:.2f} seconds')
"
```

## 🐛 Debugging Tips

### Common Commands for Troubleshooting

#### Check Dependencies
```bash
# Verify all packages are installed
python -c "
packages = ['flask', 'sklearn', 'numpy', 'pandas', 'pickle']
for pkg in packages:
    try:
        __import__(pkg)
        print(f'✅ {pkg} is installed')
    except ImportError:
        print(f'❌ {pkg} is missing')
"
```

#### Test Model Compatibility
```bash
# Check model file
python -c "
import pickle
import os
if os.path.exists('phishing_model.pkl'):
    try:
        with open('phishing_model.pkl', 'rb') as f:
            model = pickle.load(f)
        print(f'✅ Model loaded: {type(model)}')
        print(f'Features expected: {getattr(model, \"n_features_in_\", \"Unknown\")}')
    except Exception as e:
        print(f'❌ Model error: {e}')
else:
    print('❌ Model file not found')
"
```

#### Validate Templates
```bash
# Check template files exist
python -c "
import os
templates = ['templates/base.html', 'templates/index.html', 'templates/result.html']
for template in templates:
    if os.path.exists(template):
        print(f'✅ {template} exists')
    else:
        print(f'❌ {template} missing')
"
```

This comprehensive guide should help anyone understand and work with the codebase effectively!
