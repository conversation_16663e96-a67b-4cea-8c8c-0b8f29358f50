# Phishing Detection Flask Application

A Flask web application that uses machine learning to detect potentially phishing URLs. The application analyzes URL characteristics and provides real-time predictions about whether a URL is legitimate or potentially malicious.

## Features

- **Web Interface**: Clean, responsive HTML form for URL input
- **Machine Learning**: Uses a trained model to analyze URL features
- **Real-time Analysis**: Instant predictions with detailed results
- **API Endpoint**: RESTful API for programmatic access
- **Error Handling**: Robust error handling for invalid inputs
- **Bootstrap UI**: Modern, mobile-friendly interface

## URL Features Analyzed

The model analyzes the following URL characteristics:

1. **URL Length**: Total character count
2. **@ Symbol Count**: Number of '@' characters (often used in phishing)
3. **Dash Count**: Number of '-' characters in the URL
4. **HTTPS Presence**: Whether the URL uses secure HTTPS protocol
5. **Domain Dots**: Number of dots in the domain name

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Create a sample model** (if you don't have phishing_model.pkl):
   ```bash
   python create_sample_model.py
   ```

4. **Run the application**:
   ```bash
   python app.py
   ```

5. **Access the application**:
   Open your browser and go to `http://localhost:5000`

## Usage

### Web Interface

1. Navigate to `http://localhost:5000`
2. Enter a URL in the input field
3. Click "Check URL" to get the prediction
4. View the result with safety recommendations

### API Endpoint

Send POST requests to `/api/predict` with JSON data:

```bash
curl -X POST http://localhost:5000/api/predict \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'
```

Response format:
```json
{
  "url": "https://example.com",
  "prediction": "✅ Legitimate",
  "confidence_class": "success",
  "features": [18, 0, 0, 1, 1]
}
```

## File Structure

```
phishing-detection/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── create_sample_model.py # Script to generate sample model
├── phishing_model.pkl     # Trained ML model (generated)
├── templates/
│   ├── base.html         # Base template with styling
│   ├── index.html        # Main form page
│   └── result.html       # Results display page
└── README.md             # This file
```

## Model Requirements

The application expects a scikit-learn model saved as `phishing_model.pkl` that:

- Accepts 5 features in the specified order
- Returns binary predictions (0 = legitimate, 1 = phishing)
- Is compatible with scikit-learn's `.predict()` method

## Development

The application runs in debug mode by default for development. For production:

1. Set `debug=False` in `app.run()`
2. Change the secret key in production
3. Use a production WSGI server like Gunicorn
4. Implement proper logging and monitoring

## Error Handling

The application includes comprehensive error handling for:

- Missing or invalid URLs
- Model loading failures
- Feature extraction errors
- Prediction failures
- Invalid API requests

## Security Notes

- This is a demonstration application
- Always verify suspicious URLs through multiple sources
- The model's predictions should not be the sole basis for security decisions
- Consider implementing rate limiting for production use

## Contributing

To improve the model or add features:

1. Update the feature extraction logic in `extract_url_features()`
2. Retrain the model with new features
3. Update the model file
4. Test thoroughly with various URL types
