"""
Test script for AI-integrated phishing detection
Tests both traditional ML and AI-powered analysis
"""

import requests
import json
import time

# Test URLs for different scenarios
test_urls = [
    {
        "url": "https://google.com",
        "expected": "legitimate",
        "description": "Popular legitimate site"
    },
    {
        "url": "https://github.com",
        "expected": "legitimate", 
        "description": "Developer platform"
    },
    {
        "url": "http://<EMAIL>",
        "expected": "phishing",
        "description": "Suspicious PayPal impersonation"
    },
    {
        "url": "https://secure-bank-login.suspicious-domain.com",
        "expected": "phishing",
        "description": "Fake banking site"
    },
    {
        "url": "http://bit.ly/suspicious-link",
        "expected": "suspicious",
        "description": "Shortened URL (potentially suspicious)"
    }
]

def test_traditional_api():
    """Test the traditional ML + AI combined endpoint"""
    print("🔬 Testing Traditional ML + AI Combined Analysis")
    print("=" * 60)
    
    for i, test_case in enumerate(test_urls, 1):
        try:
            print(f"\nTest {i}: {test_case['description']}")
            print(f"URL: {test_case['url']}")
            
            response = requests.post(
                "http://localhost:5000/api/predict",
                json={"url": test_case['url']},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ ML Prediction: {result['prediction']}")
                print(f"📊 Features: {result['features']}")
                
                if 'ai_analysis' in result and result['ai_analysis']:
                    ai = result['ai_analysis']
                    print(f"🤖 AI Assessment: {'Phishing' if ai.get('is_phishing') else 'Legitimate'}")
                    print(f"🎯 AI Confidence: {ai.get('confidence', 0)}%")
                    
                    if ai.get('reasons'):
                        print("📝 AI Reasons:")
                        for reason in ai['reasons'][:3]:  # Show first 3 reasons
                            print(f"   • {reason}")
                    
                    if ai.get('recommendation'):
                        print(f"💡 AI Recommendation: {ai['recommendation']}")
                else:
                    print("⚠️ AI analysis not available")
                    
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out (AI analysis may take longer)")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
        
        print("-" * 40)
        time.sleep(1)  # Rate limiting

def test_ai_only_api():
    """Test the AI-only analysis endpoint"""
    print("\n🤖 Testing AI-Only Analysis Endpoint")
    print("=" * 60)
    
    for i, test_case in enumerate(test_urls[:3], 1):  # Test first 3 URLs
        try:
            print(f"\nAI Test {i}: {test_case['description']}")
            print(f"URL: {test_case['url']}")
            
            response = requests.post(
                "http://localhost:5000/api/ai-analyze",
                json={"url": test_case['url']},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                ai = result['ai_analysis']
                
                print(f"🤖 AI Assessment: {'⚠️ Phishing' if ai.get('is_phishing') else '✅ Legitimate'}")
                print(f"🎯 Confidence: {ai.get('confidence', 0)}%")
                
                if ai.get('reasons'):
                    print("📝 Analysis Reasons:")
                    for reason in ai['reasons']:
                        print(f"   • {reason}")
                
                if ai.get('recommendation'):
                    print(f"💡 Recommendation: {ai['recommendation']}")
                    
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ Request timed out")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
        
        print("-" * 40)
        time.sleep(2)  # Longer delay for AI requests

def test_server_health():
    """Check if the Flask server is running"""
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        if response.status_code == 200:
            print("✅ Flask server is running")
            return True
        else:
            print(f"⚠️ Server responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server is not accessible: {str(e)}")
        print("💡 Make sure to run: python app.py")
        return False

def main():
    """Main test function"""
    print("🧪 AI-Integrated Phishing Detection Test Suite")
    print("=" * 60)
    
    # Check server health
    if not test_server_health():
        return
    
    print("\n⚠️ Note: AI analysis requires internet connection and valid OpenAI API key")
    print("🕐 AI requests may take 10-30 seconds to complete\n")
    
    # Test traditional + AI endpoint
    test_traditional_api()
    
    # Test AI-only endpoint
    test_ai_only_api()
    
    print("\n🎉 Testing completed!")
    print("\n📊 Summary:")
    print("• Traditional ML provides fast, offline analysis")
    print("• AI analysis provides detailed reasoning and context")
    print("• Combined approach offers both speed and intelligence")
    print("• High-confidence AI results override ML predictions")

if __name__ == "__main__":
    main()
