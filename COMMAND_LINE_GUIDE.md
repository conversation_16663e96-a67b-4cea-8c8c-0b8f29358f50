# 📋 Complete Command Line Guide - AI-Enhanced Phishing Detection

## 🚀 Quick Start Commands

### 1. Initial Setup
```bash
# Navigate to project directory
cd "c:\Users\<USER>\OneDrive\Desktop\HACKTHON\phising detection project"

# Install all dependencies
pip install -r requirements.txt

# Generate sample ML model (if needed)
python create_sample_model.py

# Start the application
python app.py
```

### 2. Access the Application
```bash
# Open web interface in browser
start http://localhost:5000  # Windows
open http://localhost:5000   # Mac
xdg-open http://localhost:5000  # Linux

# Or manually navigate to: http://localhost:5000
```

## 🧪 Testing Commands

### Test Web Interface
```bash
# Quick web test
python -c "import webbrowser; webbrowser.open('http://localhost:5000')"
```

### Test API Endpoints

#### 1. Traditional ML + AI Combined Analysis
```bash
# Test legitimate URL
curl -X POST http://localhost:5000/api/predict \
  -H "Content-Type: application/json" \
  -d '{"url": "https://google.com"}'

# Test suspicious URL
curl -X POST http://localhost:5000/api/predict \
  -H "Content-Type: application/json" \
  -d '{"url": "http://<EMAIL>"}'

# PowerShell version (Windows)
$body = @{url='https://google.com'} | ConvertTo-Json
Invoke-RestMethod -Uri 'http://localhost:5000/api/predict' -Method POST -ContentType 'application/json' -Body $body
```

#### 2. AI-Only Analysis
```bash
# Pure AI analysis
curl -X POST http://localhost:5000/api/ai-analyze \
  -H "Content-Type: application/json" \
  -d '{"url": "https://suspicious-site.com"}'

# Python test
python -c "
import requests
response = requests.post('http://localhost:5000/api/ai-analyze', 
                        json={'url': 'https://github.com'})
print(response.json())
"
```

#### 3. Automated Test Suite
```bash
# Run comprehensive tests
python test_ai_integration.py

# Run original API tests
python test_api.py
```

## 🔧 Development Commands

### Environment Setup
```bash
# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
source venv/bin/activate  # Mac/Linux
venv\Scripts\activate     # Windows

# Install dependencies in virtual environment
pip install -r requirements.txt

# Deactivate when done
deactivate
```

### Dependency Management
```bash
# Install specific packages
pip install flask==2.3.3
pip install openai==0.28.1
pip install requests==2.31.0

# Check installed packages
pip list

# Check specific package
pip show flask

# Update requirements file
pip freeze > requirements.txt

# Install from requirements
pip install -r requirements.txt
```

### Model Management
```bash
# Generate new sample model
python create_sample_model.py

# Check if model exists
python -c "import os; print('Model exists:', os.path.exists('phishing_model.pkl'))"

# Test model loading
python -c "
import pickle
with open('phishing_model.pkl', 'rb') as f:
    model = pickle.load(f)
print(f'Model type: {type(model)}')
print(f'Features: {getattr(model, \"n_features_in_\", \"Unknown\")}')
"

# Backup model
copy phishing_model.pkl phishing_model_backup.pkl  # Windows
cp phishing_model.pkl phishing_model_backup.pkl    # Mac/Linux
```

## 🐛 Debugging Commands

### Check Application Health
```bash
# Test if server is running
curl -I http://localhost:5000

# Test with timeout
curl --max-time 5 http://localhost:5000

# Python health check
python -c "
import requests
try:
    response = requests.get('http://localhost:5000', timeout=5)
    print(f'✅ Server running: {response.status_code}')
except:
    print('❌ Server not responding')
"
```

### Validate Dependencies
```bash
# Check all imports
python -c "
packages = ['flask', 'sklearn', 'numpy', 'pandas', 'openai', 'requests']
for pkg in packages:
    try:
        __import__(pkg)
        print(f'✅ {pkg}')
    except ImportError as e:
        print(f'❌ {pkg}: {e}')
"

# Test OpenAI connection
python -c "
import openai
openai.api_key = 'your-key-here'
try:
    # Test with minimal request
    response = openai.ChatCompletion.create(
        model='gpt-3.5-turbo',
        messages=[{'role': 'user', 'content': 'Hi'}],
        max_tokens=5
    )
    print('✅ OpenAI API working')
except Exception as e:
    print(f'❌ OpenAI error: {e}')
"
```

### File Structure Validation
```bash
# Check all required files exist
python -c "
import os
files = [
    'app.py', 'requirements.txt', 'phishing_model.pkl',
    'templates/base.html', 'templates/index.html', 'templates/result.html'
]
for file in files:
    status = '✅' if os.path.exists(file) else '❌'
    print(f'{status} {file}')
"

# List all project files
ls -la  # Mac/Linux
dir     # Windows

# Check templates directory
ls -la templates/  # Mac/Linux
dir templates\     # Windows
```

## 🔍 Feature Testing Commands

### URL Feature Extraction
```bash
# Test feature extraction manually
python -c "
from urllib.parse import urlparse
url = 'https://suspicious-bank-login.example.com'
parsed = urlparse(url)
features = [
    len(url),                    # URL length
    url.count('@'),              # @ symbols
    url.count('-'),              # Dashes
    1 if url.startswith('https') else 0,  # HTTPS
    parsed.netloc.count('.')     # Dots in domain
]
print(f'URL: {url}')
print(f'Features: {features}')
print(f'Domain: {parsed.netloc}')
"
```

### ML Model Testing
```bash
# Test model predictions
python -c "
import pickle
import numpy as np

# Load model
with open('phishing_model.pkl', 'rb') as f:
    model = pickle.load(f)

# Test cases: [url_length, at_count, dash_count, has_https, dot_count]
test_cases = [
    [18, 0, 0, 1, 1],   # google.com
    [50, 1, 3, 0, 4],   # suspicious URL
]

for i, features in enumerate(test_cases):
    prediction = model.predict([features])[0]
    result = 'Phishing' if prediction == 1 else 'Legitimate'
    print(f'Test {i+1}: {features} -> {result}')
"
```

## 📊 Performance Testing

### Load Testing
```bash
# Simple load test
python -c "
import requests
import time
import threading

def test_request():
    try:
        response = requests.post('http://localhost:5000/api/predict',
                               json={'url': 'https://test.com'})
        print(f'Status: {response.status_code}')
    except Exception as e:
        print(f'Error: {e}')

# Run 10 concurrent requests
threads = []
for i in range(10):
    t = threading.Thread(target=test_request)
    threads.append(t)
    t.start()

for t in threads:
    t.join()
print('Load test completed')
"

# Measure response time
python -c "
import requests
import time

start = time.time()
response = requests.post('http://localhost:5000/api/predict',
                        json={'url': 'https://example.com'})
end = time.time()
print(f'Response time: {end-start:.2f} seconds')
print(f'Status: {response.status_code}')
"
```

### Memory Usage
```bash
# Check Python process memory
python -c "
import psutil
import os

process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024
print(f'Memory usage: {memory_mb:.1f} MB')
"
```

## 🔒 Security Commands

### API Key Management
```bash
# Set environment variable (recommended for production)
export OPENAI_API_KEY="your-api-key-here"  # Mac/Linux
set OPENAI_API_KEY=your-api-key-here       # Windows CMD
$env:OPENAI_API_KEY="your-api-key-here"    # Windows PowerShell

# Test environment variable
python -c "import os; print('API Key set:', bool(os.getenv('OPENAI_API_KEY')))"

# Create .env file (alternative)
echo "OPENAI_API_KEY=your-key-here" > .env

# Install python-dotenv for .env support
pip install python-dotenv
```

### Network Security
```bash
# Check open ports
netstat -an | findstr :5000  # Windows
netstat -an | grep :5000     # Mac/Linux

# Test SSL/TLS (if using HTTPS)
openssl s_client -connect localhost:5000

# Check firewall status
netsh advfirewall show allprofiles  # Windows
sudo ufw status                     # Linux
```

## 🚀 Production Deployment Commands

### Using Gunicorn (Linux/Mac)
```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Run in background
nohup gunicorn -w 4 -b 0.0.0.0:5000 app:app > app.log 2>&1 &

# Check if running
ps aux | grep gunicorn
```

### Using Waitress (Windows)
```bash
# Install Waitress
pip install waitress

# Run with Waitress
waitress-serve --host=0.0.0.0 --port=5000 app:app

# Run in background (Windows)
start /B waitress-serve --host=0.0.0.0 --port=5000 app:app
```

### Docker Deployment
```bash
# Create Dockerfile
echo "FROM python:3.11
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
EXPOSE 5000
CMD python app.py" > Dockerfile

# Build Docker image
docker build -t phishing-detector .

# Run Docker container
docker run -p 5000:5000 phishing-detector

# Run in background
docker run -d -p 5000:5000 --name phishing-app phishing-detector
```

## 📝 Logging and Monitoring

### View Logs
```bash
# View Flask logs (if running in foreground)
python app.py

# View logs with timestamp
python app.py 2>&1 | while read line; do echo "$(date): $line"; done

# Save logs to file
python app.py > app.log 2>&1

# Monitor log file
tail -f app.log  # Mac/Linux
Get-Content app.log -Wait  # Windows PowerShell
```

### System Monitoring
```bash
# Monitor CPU and memory
python -c "
import psutil
import time

while True:
    cpu = psutil.cpu_percent()
    memory = psutil.virtual_memory().percent
    print(f'CPU: {cpu}%, Memory: {memory}%')
    time.sleep(5)
"

# Monitor network connections
netstat -an | findstr :5000  # Windows
lsof -i :5000                # Mac/Linux
```

## 🛠️ Maintenance Commands

### Update Dependencies
```bash
# Check for outdated packages
pip list --outdated

# Update specific package
pip install --upgrade flask

# Update all packages (be careful!)
pip freeze | %{$_.split('==')[0]} | %{pip install --upgrade $_}  # PowerShell
pip list --outdated --format=freeze | grep -v '^\-e' | cut -d = -f 1 | xargs -n1 pip install -U  # Linux/Mac
```

### Backup and Restore
```bash
# Backup entire project
tar -czf phishing-detector-backup.tar.gz .  # Mac/Linux
Compress-Archive -Path . -DestinationPath phishing-detector-backup.zip  # Windows

# Backup just the model
copy phishing_model.pkl model-backup-$(date +%Y%m%d).pkl  # Linux/Mac
copy phishing_model.pkl model-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%.pkl  # Windows
```

### Clean Up
```bash
# Remove Python cache files
find . -name "*.pyc" -delete  # Mac/Linux
find . -name "__pycache__" -type d -exec rm -rf {} +  # Mac/Linux

# Windows PowerShell
Get-ChildItem -Path . -Recurse -Name "*.pyc" | Remove-Item
Get-ChildItem -Path . -Recurse -Name "__pycache__" | Remove-Item -Recurse

# Clean pip cache
pip cache purge
```

This comprehensive guide covers all the command-line operations needed to work with the AI-enhanced phishing detection system!
