{% extends "base.html" %}

{% block title %}Phishing Detection - Enter URL{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">🔍 Phishing Detection System</h2>
        <p class="mb-0 mt-2">Enter a URL to check if it's legitimate or potentially phishing</p>
    </div>
    <div class="card-body p-4">
        <!-- Flash messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" action="/predict" class="needs-validation" novalidate>
            <div class="mb-4">
                <label for="url" class="form-label fw-bold">URL to Check:</label>
                <input type="text" 
                       class="form-control form-control-lg" 
                       id="url" 
                       name="url" 
                       placeholder="Enter URL (e.g., https://example.com or example.com)"
                       required
                       pattern="^(https?://)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}.*">
                <div class="invalid-feedback">
                    Please enter a valid URL.
                </div>
                <div class="form-text">
                    You can enter URLs with or without http:// or https://
                </div>
            </div>
            
            <div class="d-grid">
                <button type="submit" class="btn btn-primary btn-lg">
                    🔍 Check URL
                </button>
            </div>
        </form>

        <div class="mt-4 p-3 bg-light rounded">
            <h6 class="fw-bold mb-2">How it works:</h6>
            <ul class="mb-0 small">
                <li>Our AI model analyzes URL characteristics</li>
                <li>Features include URL length, special characters, and domain structure</li>
                <li>Results are classified as either legitimate or potentially phishing</li>
                <li>Always verify suspicious URLs through multiple sources</li>
            </ul>
        </div>
    </div>
</div>

<script>
// Bootstrap form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
