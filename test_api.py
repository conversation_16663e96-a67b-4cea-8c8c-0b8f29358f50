import requests
import json

# Test the API endpoint
url = "http://localhost:5000/api/predict"
test_cases = [
    {"url": "https://google.com"},
    {"url": "http://suspicious-phishing-site.com"},
    {"url": "https://github.com"},
    {"url": "http://<EMAIL>"}
]

print("Testing Phishing Detection API:")
print("=" * 50)

for i, test_case in enumerate(test_cases, 1):
    try:
        response = requests.post(url, json=test_case)
        if response.status_code == 200:
            result = response.json()
            print(f"Test {i}: {test_case['url']}")
            print(f"  Prediction: {result['prediction']}")
            print(f"  Features: {result['features']}")
            print(f"  Confidence: {result['confidence_class']}")
        else:
            print(f"Test {i}: Error {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Test {i}: Exception - {str(e)}")
    print("-" * 30)
