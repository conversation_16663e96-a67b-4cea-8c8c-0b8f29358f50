# === FLASK PHISHING DETECTION APP ===
# Run this app: python app.py
# Make sure 'phishing_model.pkl' exists in the same directory!

from flask import Flask, render_template, request, jsonify, flash  # Web app tools
import pickle           # To load the saved ML model
import os               # File path handling
from urllib.parse import urlparse  # For breaking down URLs
import re               # Regex for validating URLs
import logging          # Logging info and errors
import openai           # OpenAI API for AI-powered analysis
import requests         # For making HTTP requests
import json             # JSON handling

# === Initialize Flask App ===
app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Used for flash messaging (⚠️ Replace in production)

# === Setup Logging ===
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# === AI Configuration ===
# OpenAI API configuration
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
openai.api_key = OPENAI_API_KEY

# === Global model variable (to store the loaded ML model) ===
model = None

# === Function: Load the model from a pickle file ===
def load_model():
    global model
    model_path = 'phishing_model.pkl'  # Ensure this file exists!

    try:
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
            logger.info("Model loaded successfully")
            return True
        else:
            logger.error(f"Model file {model_path} not found")
            return False
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return False

# === Function: Extract features from a URL ===
# These features are what the ML model uses to predict phishing
def extract_url_features(url):
    try:
        # Add default scheme if missing
        if not url.startswith(('http://', 'https://')):
            url = 'http://' + url

        parsed_url = urlparse(url)  # Break the URL into parts

        # Extract simple numeric features
        url_length = len(url)
        at_count = url.count('@')
        dash_count = url.count('-')
        has_https = 1 if url.startswith('https://') else 0
        domain = parsed_url.netloc
        dot_count_in_domain = domain.count('.')

        features = [url_length, at_count, dash_count, has_https, dot_count_in_domain]

        logger.info(f"Extracted features for URL '{url}': {features}")
        return features

    except Exception as e:
        logger.error(f"Error extracting features from URL '{url}': {str(e)}")
        raise

# === Function: AI-powered URL analysis using OpenAI ===
def analyze_url_with_ai(url):
    """
    Use OpenAI API to analyze URL for phishing indicators
    Returns detailed analysis and confidence score
    """
    try:
        prompt = f"""
        Analyze this URL for phishing indicators: {url}

        Consider these factors:
        1. Domain legitimacy and spelling
        2. Suspicious subdomains or paths
        3. URL structure and patterns
        4. Known phishing techniques
        5. Brand impersonation attempts

        Respond with a JSON object containing:
        - "is_phishing": boolean (true if likely phishing)
        - "confidence": number (0-100, confidence percentage)
        - "reasons": array of strings (specific reasons for the assessment)
        - "recommendation": string (what user should do)

        Be thorough but concise in your analysis.
        """

        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a cybersecurity expert specializing in phishing detection. Analyze URLs and provide detailed security assessments."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=500,
            temperature=0.1
        )

        ai_response = response.choices[0].message.content

        # Try to parse JSON response
        try:
            ai_analysis = json.loads(ai_response)
        except json.JSONDecodeError:
            # Fallback if AI doesn't return valid JSON
            ai_analysis = {
                "is_phishing": False,
                "confidence": 50,
                "reasons": ["AI analysis completed but format was unclear"],
                "recommendation": "Manual verification recommended"
            }

        logger.info(f"AI analysis completed for URL: {url}")
        return ai_analysis

    except Exception as e:
        logger.error(f"Error in AI analysis: {str(e)}")
        # Return safe fallback
        return {
            "is_phishing": False,
            "confidence": 0,
            "reasons": [f"AI analysis failed: {str(e)}"],
            "recommendation": "Use traditional ML model results"
        }

# === Function: Predict if the URL is phishing or legitimate (Enhanced with AI) ===
def predict_phishing(url, use_ai=True):
    global model

    if model is None:
        raise Exception("Model not loaded")

    try:
        # Get traditional ML prediction
        features = extract_url_features(url)
        ml_prediction = model.predict([features])[0]

        # Get AI analysis if enabled
        ai_analysis = None
        if use_ai:
            ai_analysis = analyze_url_with_ai(url)

        # Combine ML and AI results
        if ai_analysis and ai_analysis.get('confidence', 0) > 70:
            # High confidence AI result - use AI prediction
            if ai_analysis['is_phishing']:
                final_prediction = "⚠️ Phishing (AI Detected)"
                confidence_class = "danger"
            else:
                final_prediction = "✅ Legitimate (AI Verified)"
                confidence_class = "success"
        else:
            # Use traditional ML prediction
            if ml_prediction == 1:
                final_prediction = "⚠️ Phishing"
                confidence_class = "danger"
            else:
                final_prediction = "✅ Legitimate"
                confidence_class = "success"

        return final_prediction, confidence_class, ai_analysis

    except Exception as e:
        logger.error(f"Error making prediction: {str(e)}")
        raise

# === Route: Homepage ===
@app.route('/')
def index():
    return render_template('index.html')  # Render main input page

# === Route: Handle form submission (HTML) ===
@app.route('/predict', methods=['POST'])
def predict():
    try:
        url = request.form.get('url', '').strip()  # Get user input

        if not url:
            flash('Please enter a URL', 'error')
            return render_template('index.html')

        # Basic regex check for valid domain structure
        if not re.match(r'^(https?://)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', url):
            flash('Please enter a valid URL', 'error')
            return render_template('index.html')

        prediction_text, confidence_class, ai_analysis = predict_phishing(url)

        return render_template('result.html',
                               url=url,
                               prediction=prediction_text,
                               confidence_class=confidence_class,
                               ai_analysis=ai_analysis)

    except Exception as e:
        logger.error(f"Error in prediction route: {str(e)}")
        flash(f'Error processing URL: {str(e)}', 'error')
        return render_template('index.html')

# === API Route: Accepts JSON POST requests for programmatic access ===
# Test using: curl -X POST http://localhost:5000/api/predict -H "Content-Type: application/json" -d '{"url":"http://example.com"}'
@app.route('/api/predict', methods=['POST'])
def api_predict():
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({'error': 'URL is required'}), 400

        url = data['url'].strip()

        if not url:
            return jsonify({'error': 'URL cannot be empty'}), 400

        prediction_text, confidence_class, ai_analysis = predict_phishing(url)

        return jsonify({
            'url': url,
            'prediction': prediction_text,
            'confidence_class': confidence_class,
            'features': extract_url_features(url),
            'ai_analysis': ai_analysis
        })

    except Exception as e:
        logger.error(f"Error in API prediction: {str(e)}")
        return jsonify({'error': str(e)}), 500

# === API Route: AI-only analysis ===
@app.route('/api/ai-analyze', methods=['POST'])
def ai_analyze():
    """AI-powered URL analysis endpoint"""
    try:
        data = request.get_json()

        if not data or 'url' not in data:
            return jsonify({'error': 'URL is required'}), 400

        url = data['url'].strip()

        if not url:
            return jsonify({'error': 'URL cannot be empty'}), 400

        # Get AI analysis only
        ai_analysis = analyze_url_with_ai(url)

        return jsonify({
            'url': url,
            'ai_analysis': ai_analysis,
            'features': extract_url_features(url)
        })

    except Exception as e:
        logger.error(f"Error in AI analysis: {str(e)}")
        return jsonify({'error': str(e)}), 500

# === Start the Flask app ===
# Run this script from terminal: python app.py
# Access at: http://localhost:5000/
if __name__ == '__main__':
    if load_model():
        print("✅ Model loaded successfully. Starting Flask app...")
        app.run(debug=True, host='0.0.0.0', port=5000)  # Accessible locally and on network
    else:
        print("❌ Failed to load model. Please ensure 'phishing_model.pkl' exists in the project directory.")
        print("The app will not start without a valid model file.")
