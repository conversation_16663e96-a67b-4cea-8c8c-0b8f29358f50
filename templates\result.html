{% extends "base.html" %}

{% block title %}Phishing Detection - Result{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h2 class="mb-0">🔍 Detection Result</h2>
    </div>
    <div class="card-body p-4">
        <!-- URL being checked -->
        <div class="mb-4">
            <h6 class="fw-bold text-muted">URL Checked:</h6>
            <div class="p-3 bg-light rounded">
                <code class="text-dark">{{ url }}</code>
            </div>
        </div>

        <!-- Result -->
        <div class="result-card">
            {% if confidence_class == 'danger' %}
                <div class="alert phishing-result text-center p-4">
                    <h3 class="mb-2">{{ prediction }}</h3>
                    <p class="mb-0">This URL shows characteristics commonly associated with phishing websites. Exercise caution!</p>
                </div>
                <div class="alert alert-warning">
                    <h6 class="fw-bold">⚠️ Safety Recommendations:</h6>
                    <ul class="mb-0">
                        <li>Do not enter personal information on this website</li>
                        <li>Verify the URL through official channels</li>
                        <li>Check for spelling errors in the domain name</li>
                        <li>Look for secure connection indicators (HTTPS, padlock icon)</li>
                    </ul>
                </div>
            {% else %}
                <div class="alert legitimate-result text-center p-4">
                    <h3 class="mb-2">{{ prediction }}</h3>
                    <p class="mb-0">This URL appears to be legitimate based on our analysis.</p>
                </div>
                <div class="alert alert-info">
                    <h6 class="fw-bold">ℹ️ Note:</h6>
                    <p class="mb-0">While our analysis suggests this URL is legitimate, always remain cautious when entering sensitive information online. Verify the website's authenticity through official channels when in doubt.</p>
                </div>
            {% endif %}
        </div>

        <!-- Action buttons -->
        <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
            <a href="/" class="btn btn-primary">
                🔍 Check Another URL
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="copyResult()">
                📋 Copy Result
            </button>
        </div>

        <!-- AI Analysis Section -->
        {% if ai_analysis %}
        <div class="mt-4 p-3 bg-info bg-opacity-10 rounded border border-info">
            <h6 class="fw-bold mb-3 text-info">🤖 AI-Powered Analysis</h6>

            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2"><strong>AI Confidence:</strong>
                        <span class="badge bg-{{ 'danger' if ai_analysis.confidence > 70 and ai_analysis.is_phishing else 'success' if ai_analysis.confidence > 70 else 'warning' }}">
                            {{ ai_analysis.confidence }}%
                        </span>
                    </p>
                </div>
                <div class="col-md-6">
                    <p class="mb-2"><strong>AI Assessment:</strong>
                        <span class="text-{{ 'danger' if ai_analysis.is_phishing else 'success' }}">
                            {{ '⚠️ Suspicious' if ai_analysis.is_phishing else '✅ Appears Safe' }}
                        </span>
                    </p>
                </div>
            </div>

            {% if ai_analysis.reasons %}
            <div class="mt-3">
                <strong>Analysis Details:</strong>
                <ul class="mt-2 mb-2">
                    {% for reason in ai_analysis.reasons %}
                    <li class="small">{{ reason }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}

            {% if ai_analysis.recommendation %}
            <div class="mt-3 p-2 bg-light rounded">
                <strong>AI Recommendation:</strong>
                <p class="mb-0 small text-muted">{{ ai_analysis.recommendation }}</p>
            </div>
            {% endif %}
        </div>
        {% endif %}

        <!-- Traditional ML Analysis -->
        <div class="mt-4 p-3 bg-light rounded">
            <h6 class="fw-bold mb-2">📊 Machine Learning Analysis:</h6>
            <p class="mb-2 small">Our traditional ML model analyzes these URL characteristics:</p>
            <ul class="mb-0 small">
                <li>URL length and structure</li>
                <li>Presence of suspicious characters</li>
                <li>Domain characteristics</li>
                <li>Security protocol usage</li>
            </ul>
        </div>
    </div>
</div>

<script>
function copyResult() {
    const resultText = `URL: {{ url }}\nResult: {{ prediction }}\nAnalyzed by Phishing Detection System`;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(resultText).then(function() {
            // Show success message
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '✅ Copied!';
            btn.classList.add('btn-success');
            btn.classList.remove('btn-outline-secondary');
            
            setTimeout(function() {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
            }, 2000);
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = resultText;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        alert('Result copied to clipboard!');
    }
}
</script>
{% endblock %}
