# 🚀 Flask Phishing Detection App - Complete Setup Guide

## 📋 Table of Contents
1. [Project Overview](#project-overview)
2. [Prerequisites](#prerequisites)
3. [Installation Steps](#installation-steps)
4. [Running the Application](#running-the-application)
5. [Testing the Application](#testing-the-application)
6. [Project Structure](#project-structure)
7. [Troubleshooting](#troubleshooting)

## 🎯 Project Overview

This is a Flask web application that uses machine learning to detect phishing URLs. It analyzes URL characteristics and predicts whether a URL is legitimate or potentially malicious.

### Key Features:
- ✅ Web interface for URL analysis
- ✅ RESTful API for programmatic access
- ✅ Machine learning model for predictions
- ✅ Bootstrap-styled responsive UI
- ✅ Comprehensive error handling

## 🔧 Prerequisites

Before starting, ensure you have:

```bash
# Check Python version (3.7+ required)
python --version

# Check pip is installed
pip --version
```

## 📦 Installation Steps

### Step 1: Navigate to Project Directory
```bash
# Change to your project directory
cd "c:\Users\<USER>\OneDrive\Desktop\HACKTHON\phising detection project"

# Or create a new directory
mkdir phishing-detection
cd phishing-detection
```

### Step 2: Install Dependencies
```bash
# Install all required Python packages
pip install -r requirements.txt

# Alternative: Install packages individually
pip install Flask==2.3.3
pip install scikit-learn==1.3.0
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install pickle-mixin==1.0.2
```

### Step 3: Generate Sample Model (if needed)
```bash
# Create a sample ML model for testing
python create_sample_model.py

# This creates 'phishing_model.pkl' file
# Output should show: "✅ Sample model saved as 'phishing_model.pkl'"
```

## 🚀 Running the Application

### Start the Flask Server
```bash
# Run the main application
python app.py

# Expected output:
# ✅ Model loaded successfully. Starting Flask app...
# * Running on http://127.0.0.1:5000
# * Debug mode: on
```

### Access the Application
```bash
# Open in browser (Windows)
start http://localhost:5000

# Open in browser (Mac)
open http://localhost:5000

# Open in browser (Linux)
xdg-open http://localhost:5000
```

## 🧪 Testing the Application

### Test Web Interface
1. Open http://localhost:5000 in browser
2. Enter a URL (e.g., "https://google.com")
3. Click "Check URL"
4. View results

### Test API Endpoint
```bash
# Test with curl (if available)
curl -X POST http://localhost:5000/api/predict \
  -H "Content-Type: application/json" \
  -d '{"url": "https://google.com"}'

# Test with Python script
python test_api.py

# Test with PowerShell (Windows)
$body = @{url='https://google.com'} | ConvertTo-Json
Invoke-RestMethod -Uri 'http://localhost:5000/api/predict' -Method POST -ContentType 'application/json' -Body $body
```

### Expected API Response
```json
{
  "url": "https://google.com",
  "prediction": "✅ Legitimate",
  "confidence_class": "success",
  "features": [18, 0, 0, 1, 1]
}
```

## 📁 Project Structure

```
phishing-detection/
├── 📄 app.py                 # Main Flask application
├── 📄 requirements.txt       # Python dependencies
├── 📄 phishing_model.pkl     # Trained ML model
├── 📄 create_sample_model.py # Model generation script
├── 📄 test_api.py            # API testing script
├── 📄 README.md              # Project documentation
├── 📄 SETUP_GUIDE.md         # This setup guide
├── 📄 CODE_EXPLANATION.md    # Detailed code explanations
└── 📁 templates/             # HTML templates
    ├── 📄 base.html          # Base template with styling
    ├── 📄 index.html         # Main form page
    └── 📄 result.html        # Results display page
```

## 🔍 Understanding the Code

### Main Application Flow
1. **Model Loading**: `load_model()` loads the ML model
2. **Feature Extraction**: `extract_url_features()` analyzes URL
3. **Prediction**: `predict_phishing()` makes classification
4. **Web Routes**: Handle user requests and responses

### URL Features Analyzed
- Length of URL
- Number of '@' characters
- Number of '-' characters  
- HTTPS presence (1/0)
- Dots in domain name

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Issue: "Model file not found"
```bash
# Solution: Generate the model
python create_sample_model.py
```

#### Issue: "Module not found" errors
```bash
# Solution: Install missing packages
pip install flask scikit-learn numpy pandas
```

#### Issue: "Port already in use"
```bash
# Solution: Kill existing process or use different port
# Windows:
netstat -ano | findstr :5000
taskkill /PID <PID_NUMBER> /F

# Mac/Linux:
lsof -ti:5000 | xargs kill -9
```

#### Issue: Permission errors
```bash
# Solution: Run with appropriate permissions
# Windows (as Administrator):
python app.py

# Mac/Linux:
sudo python app.py
```

### Debug Mode
```bash
# Enable detailed error messages
export FLASK_DEBUG=1  # Mac/Linux
set FLASK_DEBUG=1     # Windows CMD
$env:FLASK_DEBUG=1    # Windows PowerShell

python app.py
```

## 📞 Getting Help

If you encounter issues:

1. **Check the logs**: Look at terminal output for error messages
2. **Verify file structure**: Ensure all files are in correct locations
3. **Test dependencies**: Run `pip list` to verify installations
4. **Check Python version**: Ensure Python 3.7+ is installed

## 🎉 Success Indicators

You'll know everything is working when you see:
- ✅ "Model loaded successfully" message
- ✅ Flask server running on port 5000
- ✅ Web interface accessible at localhost:5000
- ✅ API returning JSON responses
- ✅ Predictions showing "Legitimate" or "Phishing"
