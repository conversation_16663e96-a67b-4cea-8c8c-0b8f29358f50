"""
<PERSON><PERSON><PERSON> to create a sample phishing detection model for testing purposes.
This creates a simple model that can be used to test the Flask application.
"""

import pickle
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split

def create_sample_model():
    """
    Create a sample phishing detection model with synthetic data
    Features: [url_length, at_count, dash_count, has_https, dot_count_in_domain]
    """
    
    # Generate synthetic training data
    np.random.seed(42)
    n_samples = 1000
    
    # Create features for legitimate URLs
    legitimate_features = []
    for _ in range(n_samples // 2):
        url_length = np.random.normal(30, 10)  # Shorter URLs tend to be legitimate
        at_count = 0 if np.random.random() > 0.1 else 1  # Rarely have @ symbols
        dash_count = np.random.poisson(2)  # Few dashes
        has_https = 1 if np.random.random() > 0.3 else 0  # Often use HTTPS
        dot_count = np.random.choice([1, 2, 3], p=[0.6, 0.3, 0.1])  # Usually 1-2 dots
        
        legitimate_features.append([max(10, url_length), at_count, dash_count, has_https, dot_count])
    
    # Create features for phishing URLs
    phishing_features = []
    for _ in range(n_samples // 2):
        url_length = np.random.normal(60, 20)  # Longer URLs
        at_count = np.random.choice([0, 1, 2], p=[0.5, 0.4, 0.1])  # More @ symbols
        dash_count = np.random.poisson(5)  # More dashes
        has_https = 1 if np.random.random() > 0.7 else 0  # Less likely to use HTTPS
        dot_count = np.random.choice([2, 3, 4, 5], p=[0.3, 0.4, 0.2, 0.1])  # More dots
        
        phishing_features.append([max(10, url_length), at_count, dash_count, has_https, dot_count])
    
    # Combine features and labels
    X = np.array(legitimate_features + phishing_features)
    y = np.array([0] * (n_samples // 2) + [1] * (n_samples // 2))  # 0 = legitimate, 1 = phishing
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    model = RandomForestClassifier(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # Test accuracy
    accuracy = model.score(X_test, y_test)
    print(f"Model accuracy on test set: {accuracy:.2f}")
    
    # Save model
    with open('phishing_model.pkl', 'wb') as f:
        pickle.dump(model, f)
    
    print("✅ Sample model saved as 'phishing_model.pkl'")
    print("\nModel expects features in this order:")
    print("1. URL length")
    print("2. Number of '@' characters")
    print("3. Number of '-' characters") 
    print("4. Has HTTPS (1 if yes, 0 if no)")
    print("5. Number of dots in domain")
    
    # Test with sample URLs
    print("\n🧪 Testing with sample URLs:")
    test_cases = [
        ("https://google.com", [18, 0, 0, 1, 1]),
        ("http://secure-bank-login.suspicious-domain.com", [48, 0, 3, 0, 3]),
        ("https://paypal.com", [18, 0, 0, 1, 1]),
        ("http://<EMAIL>", [52, 1, 3, 0, 4])
    ]
    
    for url, features in test_cases:
        prediction = model.predict([features])[0]
        result = "Phishing" if prediction == 1 else "Legitimate"
        print(f"URL: {url}")
        print(f"Features: {features}")
        print(f"Prediction: {result}")
        print("-" * 50)

if __name__ == "__main__":
    create_sample_model()
